{"version": 3, "file": "Index45-FDUCI7JC.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Index45.js"], "sourcesContent": ["import { create_ssr_component, escape, add_attribute, validate_component } from \"svelte/internal\";\nimport { createEventDispatcher, onMount } from \"svelte\";\nimport { S as Static } from \"./client.js\";\nimport Index$1 from \"./Index24.js\";\nconst css = {\n  code: \"@media(max-width: 768px){.sidebar.svelte-1hez9vf.svelte-1hez9vf{width:100vw !important}.sidebar.svelte-1hez9vf.svelte-1hez9vf:not(.right){left:-100vw !important}.sidebar.right.svelte-1hez9vf.svelte-1hez9vf{right:-100vw !important}.sidebar.svelte-1hez9vf.svelte-1hez9vf:not(.reduce-motion){transition:transform 0.3s ease-in-out !important}.sidebar-parent{padding-left:0 !important;padding-right:0 !important}.sidebar-parent:has(.sidebar.open){padding-left:0 !important;padding-right:0 !important}.sidebar.open.svelte-1hez9vf.svelte-1hez9vf{z-index:1001 !important}}.sidebar-parent{display:flex !important;padding-left:0;padding-right:0}.sidebar-parent:not(.reduce-motion){transition:padding-left 0.3s ease-in-out,\\n\t\t\tpadding-right 0.3s ease-in-out}.sidebar-parent:has(.sidebar.open:not(.right)){padding-left:var(--overlap-amount)}.sidebar-parent:has(.sidebar.open.right){padding-right:var(--overlap-amount)}.sidebar.svelte-1hez9vf.svelte-1hez9vf{display:flex;flex-direction:column;position:fixed;top:0;height:100%;background-color:var(--background-fill-secondary);transform:translateX(0%);z-index:1000}.sidebar.svelte-1hez9vf.svelte-1hez9vf:not(.reduce-motion){transition:transform 0.3s ease-in-out}.sidebar.open.svelte-1hez9vf.svelte-1hez9vf:not(.right){transform:translateX(100%);box-shadow:var(--size-1) 0 var(--size-1) rgba(0, 0, 0, 0.05)}.sidebar.open.right.svelte-1hez9vf.svelte-1hez9vf{transform:translateX(-100%);box-shadow:calc(var(--size-1) * -1) 0 var(--size-1) rgba(0, 0, 0, 0.05)}.toggle-button.svelte-1hez9vf.svelte-1hez9vf{position:absolute;top:var(--size-4);background:var(--background-fill-secondary);border:1px solid var(--border-color-primary);cursor:pointer;padding:var(--size-2);display:flex;align-items:center;justify-content:center;width:var(--size-7);height:var(--size-8);z-index:1001;border-radius:0}.toggle-button.svelte-1hez9vf.svelte-1hez9vf:not(.reduce-motion){transition:all 0.3s ease-in-out}.sidebar.svelte-1hez9vf:not(.right) .toggle-button.svelte-1hez9vf{left:100%;border-radius:0 var(--size-8) var(--size-8) 0;border-left:none}.sidebar.right.svelte-1hez9vf .toggle-button.svelte-1hez9vf{right:100%;transform:rotate(180deg);border-radius:0 var(--size-8) var(--size-8) 0;border-left:none}.open.svelte-1hez9vf:not(.right) .toggle-button.svelte-1hez9vf{right:0;left:auto;transform:rotate(180deg);border-radius:0 var(--size-8) var(--size-8) 0;border-left:none;border-right:1px solid var(--border-color-primary)}.open.right.svelte-1hez9vf .toggle-button.svelte-1hez9vf{left:0;right:auto;transform:rotate(0deg);border-radius:0 var(--size-8) var(--size-8) 0;border-left:none;border-right:1px solid var(--border-color-primary)}.chevron.svelte-1hez9vf.svelte-1hez9vf{position:relative;display:flex;align-items:center;justify-content:center;padding-right:8px}.chevron-left.svelte-1hez9vf.svelte-1hez9vf{position:relative;width:var(--size-3);height:var(--size-3);border-top:var(--size-0-5) solid var(--body-text-color);border-right:var(--size-0-5) solid var(--body-text-color);transform:rotate(45deg)}.sidebar-content.svelte-1hez9vf.svelte-1hez9vf{padding:var(--size-5);padding-right:var(--size-8);overflow-y:auto}.sidebar.right.svelte-1hez9vf .sidebar-content.svelte-1hez9vf{padding-left:var(--size-8)}\",\n  map: '{\"version\":3,\"file\":\"Sidebar.svelte\",\"sources\":[\"Sidebar.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { createEventDispatcher, onMount } from \\\\\"svelte\\\\\";\\\\nconst dispatch = createEventDispatcher();\\\\nexport let open = true;\\\\nexport let width;\\\\nexport let position = \\\\\"left\\\\\";\\\\nlet mounted = false;\\\\nlet _open = false;\\\\nlet sidebar_div;\\\\nlet overlap_amount = 0;\\\\nlet width_css = typeof width === \\\\\"number\\\\\" ? `${width}px` : width;\\\\nlet prefersReducedMotion;\\\\nfunction check_overlap() {\\\\n    if (!sidebar_div.closest(\\\\\".wrap\\\\\"))\\\\n        return;\\\\n    const parent_rect = sidebar_div.closest(\\\\\".wrap\\\\\")?.getBoundingClientRect();\\\\n    if (!parent_rect)\\\\n        return;\\\\n    const sidebar_rect = sidebar_div.getBoundingClientRect();\\\\n    const available_space = position === \\\\\"left\\\\\" ? parent_rect.left : window.innerWidth - parent_rect.right;\\\\n    overlap_amount = Math.max(0, sidebar_rect.width - available_space + 30);\\\\n}\\\\nonMount(() => {\\\\n    sidebar_div.closest(\\\\\".wrap\\\\\")?.classList.add(\\\\\"sidebar-parent\\\\\");\\\\n    check_overlap();\\\\n    window.addEventListener(\\\\\"resize\\\\\", check_overlap);\\\\n    const update_parent_overlap = () => {\\\\n        document.documentElement.style.setProperty(\\\\\"--overlap-amount\\\\\", `${overlap_amount}px`);\\\\n    };\\\\n    update_parent_overlap();\\\\n    mounted = true;\\\\n    const mediaQuery = window.matchMedia(\\\\\"(prefers-reduced-motion: reduce)\\\\\");\\\\n    prefersReducedMotion = mediaQuery.matches;\\\\n    const updateMotionPreference = (e) => {\\\\n        prefersReducedMotion = e.matches;\\\\n    };\\\\n    mediaQuery.addEventListener(\\\\\"change\\\\\", updateMotionPreference);\\\\n    return () => {\\\\n        window.removeEventListener(\\\\\"resize\\\\\", check_overlap);\\\\n        mediaQuery.removeEventListener(\\\\\"change\\\\\", updateMotionPreference);\\\\n    };\\\\n});\\\\n$: if (mounted)\\\\n    _open = open;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass=\\\\\"sidebar\\\\\"\\\\n\\\\tclass:open={_open}\\\\n\\\\tclass:right={position === \\\\\"right\\\\\"}\\\\n\\\\tclass:reduce-motion={prefersReducedMotion}\\\\n\\\\tbind:this={sidebar_div}\\\\n\\\\tstyle=\\\\\"width: {width_css}; {position}: calc({width_css} * -1)\\\\\"\\\\n>\\\\n\\\\t<button\\\\n\\\\t\\\\ton:click={() => {\\\\n\\\\t\\\\t\\\\t_open = !_open;\\\\n\\\\t\\\\t\\\\topen = _open;\\\\n\\\\t\\\\t\\\\tif (_open) {\\\\n\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"expand\\\\\");\\\\n\\\\t\\\\t\\\\t} else {\\\\n\\\\t\\\\t\\\\t\\\\tdispatch(\\\\\"collapse\\\\\");\\\\n\\\\t\\\\t\\\\t}\\\\n\\\\t\\\\t}}\\\\n\\\\t\\\\tclass=\\\\\"toggle-button\\\\\"\\\\n\\\\t\\\\taria-label=\\\\\"Toggle Sidebar\\\\\"\\\\n\\\\t>\\\\n\\\\t\\\\t<div class=\\\\\"chevron\\\\\">\\\\n\\\\t\\\\t\\\\t<span class=\\\\\"chevron-left\\\\\"></span>\\\\n\\\\t\\\\t</div>\\\\n\\\\t</button>\\\\n\\\\t<div class=\\\\\"sidebar-content\\\\\">\\\\n\\\\t\\\\t<slot />\\\\n\\\\t</div>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t/* Mobile styles (≤ 768px) */\\\\n\\\\t@media (max-width: 768px) {\\\\n\\\\t\\\\t.sidebar {\\\\n\\\\t\\\\t\\\\twidth: 100vw !important;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.sidebar:not(.right) {\\\\n\\\\t\\\\t\\\\tleft: -100vw !important;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.sidebar.right {\\\\n\\\\t\\\\t\\\\tright: -100vw !important;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t.sidebar:not(.reduce-motion) {\\\\n\\\\t\\\\t\\\\ttransition: transform 0.3s ease-in-out !important;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t:global(.sidebar-parent) {\\\\n\\\\t\\\\t\\\\tpadding-left: 0 !important;\\\\n\\\\t\\\\t\\\\tpadding-right: 0 !important;\\\\n\\\\t\\\\t}\\\\n\\\\n\\\\t\\\\t:global(.sidebar-parent:has(.sidebar.open)) {\\\\n\\\\t\\\\t\\\\tpadding-left: 0 !important;\\\\n\\\\t\\\\t\\\\tpadding-right: 0 !important;\\\\n\\\\t\\\\t}\\\\n\\\\t\\\\t.sidebar.open {\\\\n\\\\t\\\\t\\\\tz-index: 1001 !important;\\\\n\\\\t\\\\t}\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.sidebar-parent) {\\\\n\\\\t\\\\tdisplay: flex !important;\\\\n\\\\t\\\\tpadding-left: 0;\\\\n\\\\t\\\\tpadding-right: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.sidebar-parent:not(.reduce-motion)) {\\\\n\\\\t\\\\ttransition:\\\\n\\\\t\\\\t\\\\tpadding-left 0.3s ease-in-out,\\\\n\\\\t\\\\t\\\\tpadding-right 0.3s ease-in-out;\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.sidebar-parent:has(.sidebar.open:not(.right))) {\\\\n\\\\t\\\\tpadding-left: var(--overlap-amount);\\\\n\\\\t}\\\\n\\\\n\\\\t:global(.sidebar-parent:has(.sidebar.open.right)) {\\\\n\\\\t\\\\tpadding-right: var(--overlap-amount);\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar {\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\tflex-direction: column;\\\\n\\\\t\\\\tposition: fixed;\\\\n\\\\t\\\\ttop: 0;\\\\n\\\\t\\\\theight: 100%;\\\\n\\\\t\\\\tbackground-color: var(--background-fill-secondary);\\\\n\\\\t\\\\ttransform: translateX(0%);\\\\n\\\\t\\\\tz-index: 1000;\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar:not(.reduce-motion) {\\\\n\\\\t\\\\ttransition: transform 0.3s ease-in-out;\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar.open:not(.right) {\\\\n\\\\t\\\\ttransform: translateX(100%);\\\\n\\\\t\\\\tbox-shadow: var(--size-1) 0 var(--size-1) rgba(0, 0, 0, 0.05);\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar.open.right {\\\\n\\\\t\\\\ttransform: translateX(-100%);\\\\n\\\\t\\\\tbox-shadow: calc(var(--size-1) * -1) 0 var(--size-1) rgba(0, 0, 0, 0.05);\\\\n\\\\t}\\\\n\\\\n\\\\t.toggle-button {\\\\n\\\\t\\\\tposition: absolute;\\\\n\\\\t\\\\ttop: var(--size-4);\\\\n\\\\t\\\\tbackground: var(--background-fill-secondary);\\\\n\\\\t\\\\tborder: 1px solid var(--border-color-primary);\\\\n\\\\t\\\\tcursor: pointer;\\\\n\\\\t\\\\tpadding: var(--size-2);\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\twidth: var(--size-7);\\\\n\\\\t\\\\theight: var(--size-8);\\\\n\\\\t\\\\tz-index: 1001;\\\\n\\\\t\\\\tborder-radius: 0;\\\\n\\\\t}\\\\n\\\\n\\\\t.toggle-button:not(.reduce-motion) {\\\\n\\\\t\\\\ttransition: all 0.3s ease-in-out;\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar:not(.right) .toggle-button {\\\\n\\\\t\\\\tleft: 100%;\\\\n\\\\t\\\\tborder-radius: 0 var(--size-8) var(--size-8) 0;\\\\n\\\\t\\\\tborder-left: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar.right .toggle-button {\\\\n\\\\t\\\\tright: 100%;\\\\n\\\\t\\\\ttransform: rotate(180deg);\\\\n\\\\t\\\\tborder-radius: 0 var(--size-8) var(--size-8) 0;\\\\n\\\\t\\\\tborder-left: none;\\\\n\\\\t}\\\\n\\\\n\\\\t.open:not(.right) .toggle-button {\\\\n\\\\t\\\\tright: 0;\\\\n\\\\t\\\\tleft: auto;\\\\n\\\\t\\\\ttransform: rotate(180deg);\\\\n\\\\t\\\\tborder-radius: 0 var(--size-8) var(--size-8) 0;\\\\n\\\\t\\\\tborder-left: none;\\\\n\\\\t\\\\tborder-right: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.open.right .toggle-button {\\\\n\\\\t\\\\tleft: 0;\\\\n\\\\t\\\\tright: auto;\\\\n\\\\t\\\\ttransform: rotate(0deg);\\\\n\\\\t\\\\tborder-radius: 0 var(--size-8) var(--size-8) 0;\\\\n\\\\t\\\\tborder-left: none;\\\\n\\\\t\\\\tborder-right: 1px solid var(--border-color-primary);\\\\n\\\\t}\\\\n\\\\n\\\\t.chevron {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\tdisplay: flex;\\\\n\\\\t\\\\talign-items: center;\\\\n\\\\t\\\\tjustify-content: center;\\\\n\\\\t\\\\tpadding-right: 8px;\\\\n\\\\t}\\\\n\\\\n\\\\t.chevron-left {\\\\n\\\\t\\\\tposition: relative;\\\\n\\\\t\\\\twidth: var(--size-3);\\\\n\\\\t\\\\theight: var(--size-3);\\\\n\\\\t\\\\tborder-top: var(--size-0-5) solid var(--body-text-color);\\\\n\\\\t\\\\tborder-right: var(--size-0-5) solid var(--body-text-color);\\\\n\\\\t\\\\ttransform: rotate(45deg);\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar-content {\\\\n\\\\t\\\\tpadding: var(--size-5);\\\\n\\\\t\\\\tpadding-right: var(--size-8);\\\\n\\\\t\\\\toverflow-y: auto;\\\\n\\\\t}\\\\n\\\\n\\\\t.sidebar.right .sidebar-content {\\\\n\\\\t\\\\tpadding-left: var(--size-8);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA6EC,MAAO,YAAY,KAAK,CAAE,CACzB,sCAAS,CACR,KAAK,CAAE,KAAK,CAAC,UACd,CAEA,sCAAQ,KAAK,MAAM,CAAE,CACpB,IAAI,CAAE,MAAM,CAAC,UACd,CAEA,QAAQ,oCAAO,CACd,KAAK,CAAE,MAAM,CAAC,UACf,CAEA,sCAAQ,KAAK,cAAc,CAAE,CAC5B,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,UACxC,CAEQ,eAAiB,CACxB,YAAY,CAAE,CAAC,CAAC,UAAU,CAC1B,aAAa,CAAE,CAAC,CAAC,UAClB,CAEQ,kCAAoC,CAC3C,YAAY,CAAE,CAAC,CAAC,UAAU,CAC1B,aAAa,CAAE,CAAC,CAAC,UAClB,CACA,QAAQ,mCAAM,CACb,OAAO,CAAE,IAAI,CAAC,UACf,CACD,CAEQ,eAAiB,CACxB,OAAO,CAAE,IAAI,CAAC,UAAU,CACxB,YAAY,CAAE,CAAC,CACf,aAAa,CAAE,CAChB,CAEQ,mCAAqC,CAC5C,UAAU,CACT,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC;AACjC,GAAG,aAAa,CAAC,IAAI,CAAC,WACrB,CAEQ,8CAAgD,CACvD,YAAY,CAAE,IAAI,gBAAgB,CACnC,CAEQ,wCAA0C,CACjD,aAAa,CAAE,IAAI,gBAAgB,CACpC,CAEA,sCAAS,CACR,OAAO,CAAE,IAAI,CACb,cAAc,CAAE,MAAM,CACtB,QAAQ,CAAE,KAAK,CACf,GAAG,CAAE,CAAC,CACN,MAAM,CAAE,IAAI,CACZ,gBAAgB,CAAE,IAAI,2BAA2B,CAAC,CAClD,SAAS,CAAE,WAAW,EAAE,CAAC,CACzB,OAAO,CAAE,IACV,CAEA,sCAAQ,KAAK,cAAc,CAAE,CAC5B,UAAU,CAAE,SAAS,CAAC,IAAI,CAAC,WAC5B,CAEA,QAAQ,mCAAK,KAAK,MAAM,CAAE,CACzB,SAAS,CAAE,WAAW,IAAI,CAAC,CAC3B,UAAU,CAAE,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAC7D,CAEA,QAAQ,KAAK,oCAAO,CACnB,SAAS,CAAE,WAAW,KAAK,CAAC,CAC5B,UAAU,CAAE,KAAK,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CACxE,CAEA,4CAAe,CACd,QAAQ,CAAE,QAAQ,CAClB,GAAG,CAAE,IAAI,QAAQ,CAAC,CAClB,UAAU,CAAE,IAAI,2BAA2B,CAAC,CAC5C,MAAM,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CAAC,CAC7C,MAAM,CAAE,OAAO,CACf,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,OAAO,CAAE,IAAI,CACb,aAAa,CAAE,CAChB,CAEA,4CAAc,KAAK,cAAc,CAAE,CAClC,UAAU,CAAE,GAAG,CAAC,IAAI,CAAC,WACtB,CAEA,uBAAQ,KAAK,MAAM,CAAC,CAAC,6BAAe,CACnC,IAAI,CAAE,IAAI,CACV,aAAa,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAC9C,WAAW,CAAE,IACd,CAEA,QAAQ,qBAAM,CAAC,6BAAe,CAC7B,KAAK,CAAE,IAAI,CACX,SAAS,CAAE,OAAO,MAAM,CAAC,CACzB,aAAa,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAC9C,WAAW,CAAE,IACd,CAEA,oBAAK,KAAK,MAAM,CAAC,CAAC,6BAAe,CAChC,KAAK,CAAE,CAAC,CACR,IAAI,CAAE,IAAI,CACV,SAAS,CAAE,OAAO,MAAM,CAAC,CACzB,aAAa,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAC9C,WAAW,CAAE,IAAI,CACjB,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnD,CAEA,KAAK,qBAAM,CAAC,6BAAe,CAC1B,IAAI,CAAE,CAAC,CACP,KAAK,CAAE,IAAI,CACX,SAAS,CAAE,OAAO,IAAI,CAAC,CACvB,aAAa,CAAE,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAC9C,WAAW,CAAE,IAAI,CACjB,YAAY,CAAE,GAAG,CAAC,KAAK,CAAC,IAAI,sBAAsB,CACnD,CAEA,sCAAS,CACR,QAAQ,CAAE,QAAQ,CAClB,OAAO,CAAE,IAAI,CACb,WAAW,CAAE,MAAM,CACnB,eAAe,CAAE,MAAM,CACvB,aAAa,CAAE,GAChB,CAEA,2CAAc,CACb,QAAQ,CAAE,QAAQ,CAClB,KAAK,CAAE,IAAI,QAAQ,CAAC,CACpB,MAAM,CAAE,IAAI,QAAQ,CAAC,CACrB,UAAU,CAAE,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,CACxD,YAAY,CAAE,IAAI,UAAU,CAAC,CAAC,KAAK,CAAC,IAAI,iBAAiB,CAAC,CAC1D,SAAS,CAAE,OAAO,KAAK,CACxB,CAEA,8CAAiB,CAChB,OAAO,CAAE,IAAI,QAAQ,CAAC,CACtB,aAAa,CAAE,IAAI,QAAQ,CAAC,CAC5B,UAAU,CAAE,IACb,CAEA,QAAQ,qBAAM,CAAC,+BAAiB,CAC/B,YAAY,CAAE,IAAI,QAAQ,CAC3B\"}'\n};\nconst Sidebar = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  createEventDispatcher();\n  let { open = true } = $$props;\n  let { width } = $$props;\n  let { position = \"left\" } = $$props;\n  let mounted = false;\n  let _open = false;\n  let sidebar_div;\n  let overlap_amount = 0;\n  let width_css = typeof width === \"number\" ? `${width}px` : width;\n  let prefersReducedMotion;\n  function check_overlap() {\n    if (!sidebar_div.closest(\".wrap\"))\n      return;\n    const parent_rect = sidebar_div.closest(\".wrap\")?.getBoundingClientRect();\n    if (!parent_rect)\n      return;\n    const sidebar_rect = sidebar_div.getBoundingClientRect();\n    const available_space = position === \"left\" ? parent_rect.left : window.innerWidth - parent_rect.right;\n    overlap_amount = Math.max(0, sidebar_rect.width - available_space + 30);\n  }\n  onMount(() => {\n    sidebar_div.closest(\".wrap\")?.classList.add(\"sidebar-parent\");\n    check_overlap();\n    window.addEventListener(\"resize\", check_overlap);\n    const update_parent_overlap = () => {\n      document.documentElement.style.setProperty(\"--overlap-amount\", `${overlap_amount}px`);\n    };\n    update_parent_overlap();\n    mounted = true;\n    const mediaQuery = window.matchMedia(\"(prefers-reduced-motion: reduce)\");\n    prefersReducedMotion = mediaQuery.matches;\n    const updateMotionPreference = (e) => {\n      prefersReducedMotion = e.matches;\n    };\n    mediaQuery.addEventListener(\"change\", updateMotionPreference);\n    return () => {\n      window.removeEventListener(\"resize\", check_overlap);\n      mediaQuery.removeEventListener(\"change\", updateMotionPreference);\n    };\n  });\n  if ($$props.open === void 0 && $$bindings.open && open !== void 0)\n    $$bindings.open(open);\n  if ($$props.width === void 0 && $$bindings.width && width !== void 0)\n    $$bindings.width(width);\n  if ($$props.position === void 0 && $$bindings.position && position !== void 0)\n    $$bindings.position(position);\n  $$result.css.add(css);\n  {\n    if (mounted)\n      _open = open;\n  }\n  return `<div class=\"${[\n    \"sidebar svelte-1hez9vf\",\n    (_open ? \"open\" : \"\") + \" \" + (position === \"right\" ? \"right\" : \"\") + \" \" + (prefersReducedMotion ? \"reduce-motion\" : \"\")\n  ].join(\" \").trim()}\" style=\"${\"width: \" + escape(width_css, true) + \"; \" + escape(position, true) + \": calc(\" + escape(width_css, true) + \" * -1)\"}\"${add_attribute(\"this\", sidebar_div, 0)}><button class=\"toggle-button svelte-1hez9vf\" aria-label=\"Toggle Sidebar\" data-svelte-h=\"svelte-12xdlyi\"><div class=\"chevron svelte-1hez9vf\"><span class=\"chevron-left svelte-1hez9vf\"></span></div></button> <div class=\"sidebar-content svelte-1hez9vf\">${slots.default ? slots.default({}) : ``}</div> </div>`;\n});\nconst Index = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { open = true } = $$props;\n  let { position = \"left\" } = $$props;\n  let { loading_status } = $$props;\n  let { gradio } = $$props;\n  let { width } = $$props;\n  let { visible = true } = $$props;\n  if ($$props.open === void 0 && $$bindings.open && open !== void 0)\n    $$bindings.open(open);\n  if ($$props.position === void 0 && $$bindings.position && position !== void 0)\n    $$bindings.position(position);\n  if ($$props.loading_status === void 0 && $$bindings.loading_status && loading_status !== void 0)\n    $$bindings.loading_status(loading_status);\n  if ($$props.gradio === void 0 && $$bindings.gradio && gradio !== void 0)\n    $$bindings.gradio(gradio);\n  if ($$props.width === void 0 && $$bindings.width && width !== void 0)\n    $$bindings.width(width);\n  if ($$props.visible === void 0 && $$bindings.visible && visible !== void 0)\n    $$bindings.visible(visible);\n  let $$settled;\n  let $$rendered;\n  let previous_head = $$result.head;\n  do {\n    $$settled = true;\n    $$result.head = previous_head;\n    $$rendered = `${validate_component(Static, \"StatusTracker\").$$render($$result, Object.assign({}, { autoscroll: gradio.autoscroll }, { i18n: gradio.i18n }, loading_status), {}, {})} ${visible ? `${validate_component(Sidebar, \"Sidebar\").$$render(\n      $$result,\n      { width, open, position },\n      {\n        open: ($$value) => {\n          open = $$value;\n          $$settled = false;\n        },\n        position: ($$value) => {\n          position = $$value;\n          $$settled = false;\n        }\n      },\n      {\n        default: () => {\n          return `${validate_component(Index$1, \"Column\").$$render($$result, {}, {}, {\n            default: () => {\n              return `${slots.default ? slots.default({}) : ``}`;\n            }\n          })}`;\n        }\n      }\n    )}` : ``}`;\n  } while (!$$settled);\n  return $$rendered;\n});\nexport {\n  Index as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAIA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,2pGAA2pG;AACnqG,EAAE,GAAG,EAAE,2pTAA2pT;AAClqT,CAAC,CAAC;AACF,MAAM,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,qBAAqB,EAAE,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AAGtC,EAAE,IAAI,WAAW,CAAC;AAElB,EAAE,IAAI,SAAS,GAAG,OAAO,KAAK,KAAK,QAAQ,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC;AAgCnE,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAKxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,wBAAwB;AAC5B,IAAI,CAAkB,EAAE,IAAI,GAAG,IAAI,QAAQ,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,CAAC,GAAG,GAAG,IAA6C,EAAE,CAAC;AAC7H,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,SAAS,EAAE,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,SAAS,GAAG,MAAM,CAAC,SAAS,EAAE,IAAI,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,aAAa,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC,CAAC,CAAC,0PAA0P,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;AAChf,CAAC,CAAC,CAAC;AACE,MAAC,KAAK,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC7E,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,QAAQ,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC;AACtC,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;AAC3B,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC;AACnC,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,cAAc,IAAI,cAAc,KAAK,KAAK,CAAC;AACjG,IAAI,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC;AAC9C,EAAE,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,MAAM,IAAI,MAAM,KAAK,KAAK,CAAC;AACzE,IAAI,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAC9B,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,OAAO,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,IAAI,OAAO,KAAK,KAAK,CAAC;AAC5E,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAChC,EAAE,IAAI,SAAS,CAAC;AAChB,EAAE,IAAI,UAAU,CAAC;AACjB,EAAE,IAAI,aAAa,GAAG,QAAQ,CAAC,IAAI,CAAC;AACpC,EAAE,GAAG;AACL,IAAI,SAAS,GAAG,IAAI,CAAC;AACrB,IAAI,QAAQ,CAAC,IAAI,GAAG,aAAa,CAAC;AAClC,IAAI,UAAU,GAAG,CAAC,EAAE,kBAAkB,CAAC,MAAM,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,UAAU,EAAE,MAAM,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ;AACvP,MAAM,QAAQ;AACd,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE;AAC/B,MAAM;AACN,QAAQ,IAAI,EAAE,CAAC,OAAO,KAAK;AAC3B,UAAU,IAAI,GAAG,OAAO,CAAC;AACzB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,QAAQ,QAAQ,EAAE,CAAC,OAAO,KAAK;AAC/B,UAAU,QAAQ,GAAG,OAAO,CAAC;AAC7B,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,SAAS;AACT,OAAO;AACP,MAAM;AACN,QAAQ,OAAO,EAAE,MAAM;AACvB,UAAU,OAAO,CAAC,EAAE,kBAAkB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,QAAQ,EAAE,EAAE,EAAE,EAAE,EAAE;AACrF,YAAY,OAAO,EAAE,MAAM;AAC3B,cAAc,OAAO,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACjE,aAAa;AACb,WAAW,CAAC,CAAC,CAAC,CAAC;AACf,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACf,GAAG,QAAQ,CAAC,SAAS,EAAE;AACvB,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC;;;;"}