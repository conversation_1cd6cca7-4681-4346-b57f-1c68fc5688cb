{"version": 3, "file": "Example18-B03urT1p.js", "sources": ["../../../../../../js/app/.svelte-kit/adapter-node/chunks/Example18.js"], "sourcesContent": ["import { create_ssr_component, validate_component } from \"svelte/internal\";\nimport { v as MarkdownCode } from \"./client.js\";\nconst css = {\n  code: \".gallery.svelte-1ayixqk{padding:var(--size-1) var(--size-2)}\",\n  map: '{\"version\":3,\"file\":\"Example.svelte\",\"sources\":[\"Example.svelte\"],\"sourcesContent\":[\"<script lang=\\\\\"ts\\\\\">import { MarkdownCode } from \\\\\"@gradio/markdown-code\\\\\";\\\\nexport let value;\\\\nexport let type;\\\\nexport let selected = false;\\\\nexport let sanitize_html;\\\\nexport let line_breaks;\\\\nexport let latex_delimiters;\\\\nexport let root;\\\\n<\\/script>\\\\n\\\\n<div\\\\n\\\\tclass:table={type === \\\\\"table\\\\\"}\\\\n\\\\tclass:gallery={type === \\\\\"gallery\\\\\"}\\\\n\\\\tclass:selected\\\\n\\\\tclass=\\\\\"prose\\\\\"\\\\n>\\\\n\\\\t<MarkdownCode\\\\n\\\\t\\\\tmessage={value ? value : \\\\\"\\\\\"}\\\\n\\\\t\\\\t{latex_delimiters}\\\\n\\\\t\\\\t{sanitize_html}\\\\n\\\\t\\\\t{line_breaks}\\\\n\\\\t\\\\tchatbot={false}\\\\n\\\\t\\\\t{root}\\\\n\\\\t/>\\\\n</div>\\\\n\\\\n<style>\\\\n\\\\t.gallery {\\\\n\\\\t\\\\tpadding: var(--size-1) var(--size-2);\\\\n\\\\t}</style>\\\\n\"],\"names\":[],\"mappings\":\"AA2BC,uBAAS,CACR,OAAO,CAAE,IAAI,QAAQ,CAAC,CAAC,IAAI,QAAQ,CACpC\"}'\n};\nconst Example = create_ssr_component(($$result, $$props, $$bindings, slots) => {\n  let { value } = $$props;\n  let { type } = $$props;\n  let { selected = false } = $$props;\n  let { sanitize_html } = $$props;\n  let { line_breaks } = $$props;\n  let { latex_delimiters } = $$props;\n  let { root } = $$props;\n  if ($$props.value === void 0 && $$bindings.value && value !== void 0)\n    $$bindings.value(value);\n  if ($$props.type === void 0 && $$bindings.type && type !== void 0)\n    $$bindings.type(type);\n  if ($$props.selected === void 0 && $$bindings.selected && selected !== void 0)\n    $$bindings.selected(selected);\n  if ($$props.sanitize_html === void 0 && $$bindings.sanitize_html && sanitize_html !== void 0)\n    $$bindings.sanitize_html(sanitize_html);\n  if ($$props.line_breaks === void 0 && $$bindings.line_breaks && line_breaks !== void 0)\n    $$bindings.line_breaks(line_breaks);\n  if ($$props.latex_delimiters === void 0 && $$bindings.latex_delimiters && latex_delimiters !== void 0)\n    $$bindings.latex_delimiters(latex_delimiters);\n  if ($$props.root === void 0 && $$bindings.root && root !== void 0)\n    $$bindings.root(root);\n  $$result.css.add(css);\n  return `<div class=\"${[\n    \"prose svelte-1ayixqk\",\n    (type === \"table\" ? \"table\" : \"\") + \" \" + (type === \"gallery\" ? \"gallery\" : \"\") + \" \" + (selected ? \"selected\" : \"\")\n  ].join(\" \").trim()}\">${validate_component(MarkdownCode, \"MarkdownCode\").$$render(\n    $$result,\n    {\n      message: value ? value : \"\",\n      latex_delimiters,\n      sanitize_html,\n      line_breaks,\n      chatbot: false,\n      root\n    },\n    {},\n    {}\n  )} </div>`;\n});\nexport {\n  Example as default\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA,MAAM,GAAG,GAAG;AACZ,EAAE,IAAI,EAAE,8DAA8D;AACtE,EAAE,GAAG,EAAE,k2BAAk2B;AACz2B,CAAC,CAAC;AACG,MAAC,OAAO,GAAG,oBAAoB,CAAC,CAAC,QAAQ,EAAE,OAAO,EAAE,UAAU,EAAE,KAAK,KAAK;AAC/E,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,OAAO,CAAC;AAClC,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,OAAO,CAAC;AAChC,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC;AACrC,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC;AACzB,EAAE,IAAI,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,KAAK,IAAI,KAAK,KAAK,KAAK,CAAC;AACtE,IAAI,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC5B,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,IAAI,OAAO,CAAC,QAAQ,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,IAAI,QAAQ,KAAK,KAAK,CAAC;AAC/E,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAClC,EAAE,IAAI,OAAO,CAAC,aAAa,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,aAAa,IAAI,aAAa,KAAK,KAAK,CAAC;AAC9F,IAAI,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;AAC5C,EAAE,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,WAAW,IAAI,WAAW,KAAK,KAAK,CAAC;AACxF,IAAI,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;AACxC,EAAE,IAAI,OAAO,CAAC,gBAAgB,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,gBAAgB,IAAI,gBAAgB,KAAK,KAAK,CAAC;AACvG,IAAI,UAAU,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC;AAClD,EAAE,IAAI,OAAO,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,IAAI,IAAI,KAAK,KAAK,CAAC;AACnE,IAAI,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACxB,EAAE,OAAO,CAAC,YAAY,EAAE;AACxB,IAAI,sBAAsB;AAC1B,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,OAAO,GAAG,EAAE,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,EAAE,CAAC,GAAG,GAAG,IAAI,QAAQ,GAAG,UAAU,GAAG,EAAE,CAAC;AACxH,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,kBAAkB,CAAC,YAAY,EAAE,cAAc,CAAC,CAAC,QAAQ;AAClF,IAAI,QAAQ;AACZ,IAAI;AACJ,MAAM,OAAO,EAAE,KAAK,GAAG,KAAK,GAAG,EAAE;AACjC,MAAM,gBAAgB;AACtB,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,IAAI;AACV,KAAK;AACL,IAAI,EAAE;AACN,IAAI,EAAE;AACN,GAAG,CAAC,OAAO,CAAC,CAAC;AACb,CAAC;;;;"}