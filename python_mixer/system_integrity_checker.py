#!/usr/bin/env python3
"""
🔍 HVAC CRM System Integrity Checker & Rebuilder
=================================================

Comprehensive system integrity checker for Gobeklitepe, Weaviate, GoSpine integration.
Checks health, rebuilds containers, tests functionality, and enhances capabilities.

Features:
- Container health monitoring and rebuilding
- Weaviate integration testing
- GoSpine API connectivity verification
- Semantic framework validation
- Performance benchmarking
- Automated fixes and enhancements
"""

import asyncio
import logging
import os
import sys
import json
import time
import subprocess
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import docker
import psutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class SystemIntegrityChecker:
    """Comprehensive system integrity checker and rebuilder."""
    
    def __init__(self):
        """Initialize the system checker."""
        self.docker_client = None
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'checks': {},
            'fixes_applied': [],
            'recommendations': []
        }
        
        # Service endpoints
        self.endpoints = {
            'weaviate': 'http://localhost:8082',
            'gospine': 'http://localhost:8080',
            'redis_local': 'http://localhost:6379',
            'redis_external': 'http://**************:3037',
            'mongodb': 'http://localhost:27017',
            'admin_interface': 'http://localhost:7861'
        }
        
        # Container names to check
        self.containers = [
            'hvac-weaviate',
            'hvac-embeddings', 
            'hvac-redis',
            'hvac-mongodb-local',
            'hvac-transcription-orchestrator',
            'hvac-simple-stt',
            'hvac-email-processor',
            'hvac-audio-converter',
            'hvac-gemma-integration'
        ]
    
    async def run_comprehensive_check(self) -> Dict[str, Any]:
        """Run comprehensive system integrity check."""
        logger.info("🚀 Starting comprehensive system integrity check...")
        
        try:
            # Initialize Docker client
            self.docker_client = docker.from_env()
            
            # Run all checks
            await self._check_docker_containers()
            await self._check_service_endpoints()
            await self._check_weaviate_health()
            await self._check_gospine_integration()
            await self._check_system_resources()
            await self._check_semantic_framework()
            
            # Apply fixes if needed
            await self._apply_automatic_fixes()
            
            # Generate recommendations
            self._generate_recommendations()
            
            # Save results
            self._save_results()
            
            logger.info("✅ System integrity check completed successfully")
            return self.results
            
        except Exception as e:
            logger.error(f"❌ System integrity check failed: {e}")
            self.results['error'] = str(e)
            return self.results
    
    async def _check_docker_containers(self):
        """Check Docker container health and status."""
        logger.info("🐳 Checking Docker containers...")
        
        container_status = {}
        
        for container_name in self.containers:
            try:
                container = self.docker_client.containers.get(container_name)
                status = {
                    'status': container.status,
                    'health': getattr(container.attrs['State'], 'Health', {}).get('Status', 'unknown'),
                    'created': container.attrs['Created'],
                    'image': container.image.tags[0] if container.image.tags else 'unknown',
                    'ports': container.ports,
                    'restart_count': container.attrs['RestartCount']
                }
                
                # Check if container is unhealthy
                if status['health'] == 'unhealthy' or status['status'] != 'running':
                    status['needs_rebuild'] = True
                    self.results['fixes_applied'].append(f"Container {container_name} marked for rebuild")
                
                container_status[container_name] = status
                
            except docker.errors.NotFound:
                container_status[container_name] = {
                    'status': 'not_found',
                    'needs_rebuild': True
                }
                self.results['fixes_applied'].append(f"Container {container_name} not found - needs creation")
            
            except Exception as e:
                container_status[container_name] = {
                    'status': 'error',
                    'error': str(e)
                }
        
        self.results['checks']['docker_containers'] = container_status
        logger.info(f"📊 Checked {len(self.containers)} containers")
    
    async def _check_service_endpoints(self):
        """Check service endpoint availability."""
        logger.info("🌐 Checking service endpoints...")
        
        endpoint_status = {}
        
        for service, url in self.endpoints.items():
            try:
                if service == 'redis_local' or service == 'redis_external':
                    # Special handling for Redis
                    status = await self._check_redis_endpoint(url)
                else:
                    response = requests.get(f"{url}/health", timeout=5)
                    status = {
                        'available': response.status_code == 200,
                        'response_time': response.elapsed.total_seconds(),
                        'status_code': response.status_code
                    }
                
                endpoint_status[service] = status
                
            except requests.exceptions.RequestException as e:
                endpoint_status[service] = {
                    'available': False,
                    'error': str(e)
                }
        
        self.results['checks']['service_endpoints'] = endpoint_status
        logger.info(f"🔗 Checked {len(self.endpoints)} service endpoints")
    
    async def _check_redis_endpoint(self, url: str) -> Dict[str, Any]:
        """Check Redis endpoint specifically."""
        try:
            import redis
            
            # Extract host and port from URL
            if '://' in url:
                url = url.split('://', 1)[1]
            
            if ':' in url:
                host, port = url.split(':', 1)
                port = int(port)
            else:
                host, port = url, 6379
            
            r = redis.Redis(host=host, port=port, socket_timeout=5)
            r.ping()
            
            return {
                'available': True,
                'response_time': 0.1,
                'info': r.info()
            }
            
        except Exception as e:
            return {
                'available': False,
                'error': str(e)
            }
    
    async def _check_weaviate_health(self):
        """Check Weaviate health and functionality."""
        logger.info("🧠 Checking Weaviate health...")
        
        weaviate_status = {}
        
        try:
            # Check basic endpoints
            endpoints_to_check = [
                '/v1/.well-known/live',
                '/v1/.well-known/ready', 
                '/v1/meta'
            ]
            
            for endpoint in endpoints_to_check:
                try:
                    response = requests.get(f"{self.endpoints['weaviate']}{endpoint}", timeout=10)
                    weaviate_status[endpoint] = {
                        'status_code': response.status_code,
                        'response_time': response.elapsed.total_seconds(),
                        'content_length': len(response.content)
                    }
                    
                    if endpoint == '/v1/meta':
                        weaviate_status[endpoint]['meta_data'] = response.json()
                        
                except Exception as e:
                    weaviate_status[endpoint] = {'error': str(e)}
            
            # Test vector operations if available
            try:
                import weaviate
                import weaviate.classes as wvc

                # Initialize Weaviate v4 client
                client = weaviate.connect_to_local(
                    host="localhost",
                    port=8082
                )

                # Test basic operations
                collections = client.collections.list_all()
                weaviate_status['schema_classes'] = len(collections)
                weaviate_status['client_connection'] = True
                weaviate_status['client_version'] = 'v4'

                # Test if client is ready
                if client.is_ready():
                    weaviate_status['ready_status'] = True
                else:
                    weaviate_status['ready_status'] = False

                # Close connection
                client.close()

            except Exception as e:
                weaviate_status['client_connection'] = False
                weaviate_status['client_error'] = str(e)
        
        except Exception as e:
            weaviate_status['error'] = str(e)
        
        self.results['checks']['weaviate_health'] = weaviate_status
        logger.info("🔍 Weaviate health check completed")
    
    async def _check_gospine_integration(self):
        """Check GoSpine API integration."""
        logger.info("🔗 Checking GoSpine integration...")
        
        gospine_status = {}
        
        try:
            # Test GoSpine API endpoints
            test_endpoints = [
                '/api/v1/health',
                '/api/v1/pipeline/stages',
                '/api/v1/scoring/criteria',
                '/api/v1/knowledge/suggestions?context=test'
            ]
            
            for endpoint in test_endpoints:
                try:
                    response = requests.get(f"{self.endpoints['gospine']}{endpoint}", timeout=10)
                    gospine_status[endpoint] = {
                        'status_code': response.status_code,
                        'response_time': response.elapsed.total_seconds(),
                        'data_available': len(response.content) > 0
                    }
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            gospine_status[endpoint]['data_summary'] = {
                                'keys': list(data.keys()) if isinstance(data, dict) else 'non_dict',
                                'size': len(str(data))
                            }
                        except:
                            pass
                            
                except Exception as e:
                    gospine_status[endpoint] = {'error': str(e)}
        
        except Exception as e:
            gospine_status['error'] = str(e)
        
        self.results['checks']['gospine_integration'] = gospine_status
        logger.info("📡 GoSpine integration check completed")
    
    async def _check_system_resources(self):
        """Check system resource usage."""
        logger.info("💻 Checking system resources...")
        
        resources = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory': {
                'total': psutil.virtual_memory().total,
                'available': psutil.virtual_memory().available,
                'percent': psutil.virtual_memory().percent
            },
            'disk': {
                'total': psutil.disk_usage('/').total,
                'free': psutil.disk_usage('/').free,
                'percent': psutil.disk_usage('/').percent
            },
            'network': dict(psutil.net_io_counters()._asdict()),
            'processes': len(psutil.pids())
        }
        
        # Check for resource constraints
        if resources['memory']['percent'] > 85:
            self.results['recommendations'].append("High memory usage detected - consider optimizing containers")
        
        if resources['disk']['percent'] > 90:
            self.results['recommendations'].append("Low disk space - cleanup recommended")
        
        self.results['checks']['system_resources'] = resources
        logger.info("📊 System resources check completed")
    
    async def _check_semantic_framework(self):
        """Check semantic framework components."""
        logger.info("🧠 Checking semantic framework...")
        
        semantic_status = {}
        
        try:
            # Check if Gobeklitepe bridge can be imported
            sys.path.append('/home/<USER>/HVAC/unifikacja/GoSpine/python_mixer')
            
            try:
                from integrations.gobeklitepe_bridge import GobeklitepeBridge, get_default_config
                semantic_status['bridge_import'] = True
                
                # Test bridge initialization
                config = get_default_config()
                config['weaviate_url'] = self.endpoints['weaviate']
                
                bridge = GobeklitepeBridge(config)
                semantic_status['bridge_creation'] = True
                
                # Test basic functionality
                test_result = await bridge.analyze_email_semantic(
                    "Test email for semantic analysis",
                    customer_id="TEST001"
                )
                
                semantic_status['semantic_analysis'] = {
                    'working': True,
                    'processing_time': test_result.processing_time,
                    'confidence': test_result.confidence_score
                }
                
            except Exception as e:
                semantic_status['bridge_error'] = str(e)
                semantic_status['bridge_import'] = False
        
        except Exception as e:
            semantic_status['framework_error'] = str(e)
        
        self.results['checks']['semantic_framework'] = semantic_status
        logger.info("🔬 Semantic framework check completed")

    async def _apply_automatic_fixes(self):
        """Apply automatic fixes for detected issues."""
        logger.info("🔧 Applying automatic fixes...")

        fixes_applied = []

        # Fix unhealthy containers
        container_checks = self.results['checks'].get('docker_containers', {})
        for container_name, status in container_checks.items():
            if status.get('needs_rebuild'):
                try:
                    await self._rebuild_container(container_name)
                    fixes_applied.append(f"Rebuilt container: {container_name}")
                except Exception as e:
                    fixes_applied.append(f"Failed to rebuild {container_name}: {e}")

        # Fix Weaviate connection issues
        weaviate_checks = self.results['checks'].get('weaviate_health', {})
        if not weaviate_checks.get('client_connection'):
            try:
                await self._fix_weaviate_connection()
                fixes_applied.append("Fixed Weaviate connection")
            except Exception as e:
                fixes_applied.append(f"Failed to fix Weaviate: {e}")

        self.results['fixes_applied'].extend(fixes_applied)
        logger.info(f"🛠️ Applied {len(fixes_applied)} automatic fixes")

    async def _rebuild_container(self, container_name: str):
        """Rebuild a specific container."""
        logger.info(f"🔄 Rebuilding container: {container_name}")

        try:
            # Stop and remove existing container
            try:
                container = self.docker_client.containers.get(container_name)
                container.stop()
                container.remove()
                logger.info(f"Removed existing container: {container_name}")
            except docker.errors.NotFound:
                pass

            # Restart with docker-compose
            if container_name == 'hvac-weaviate':
                subprocess.run([
                    'docker', 'run', '-d',
                    '--name', container_name,
                    '-p', '8082:8080',
                    '-p', '50051:50051',
                    '-e', 'QUERY_DEFAULTS_LIMIT=25',
                    '-e', 'AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true',
                    '-e', 'PERSISTENCE_DATA_PATH=/var/lib/weaviate',
                    '-e', 'DEFAULT_VECTORIZER_MODULE=none',
                    '-e', 'ENABLE_MODULES=text2vec-transformers',
                    'semitechnologies/weaviate:1.25.0'
                ], check=True)

                # Wait for container to be ready
                await asyncio.sleep(10)

        except Exception as e:
            logger.error(f"Failed to rebuild {container_name}: {e}")
            raise

    async def _fix_weaviate_connection(self):
        """Fix Weaviate connection issues."""
        logger.info("🔧 Fixing Weaviate connection...")

        # Wait for Weaviate to be ready
        max_retries = 30
        for i in range(max_retries):
            try:
                response = requests.get(f"{self.endpoints['weaviate']}/v1/.well-known/ready", timeout=5)
                if response.status_code == 200:
                    logger.info("✅ Weaviate is ready")
                    return
            except:
                pass

            await asyncio.sleep(2)

        raise Exception("Weaviate failed to become ready after 60 seconds")

    def _generate_recommendations(self):
        """Generate system improvement recommendations."""
        logger.info("💡 Generating recommendations...")

        recommendations = []

        # Analyze results and generate recommendations
        container_checks = self.results['checks'].get('docker_containers', {})
        unhealthy_containers = [name for name, status in container_checks.items()
                              if status.get('health') == 'unhealthy']

        if unhealthy_containers:
            recommendations.append(f"Monitor containers: {', '.join(unhealthy_containers)}")

        # Check service availability
        endpoint_checks = self.results['checks'].get('service_endpoints', {})
        unavailable_services = [name for name, status in endpoint_checks.items()
                               if not status.get('available')]

        if unavailable_services:
            recommendations.append(f"Restore services: {', '.join(unavailable_services)}")

        # Performance recommendations
        resources = self.results['checks'].get('system_resources', {})
        if resources.get('memory', {}).get('percent', 0) > 80:
            recommendations.append("Consider increasing system memory or optimizing containers")

        # Semantic framework recommendations
        semantic_checks = self.results['checks'].get('semantic_framework', {})
        if not semantic_checks.get('bridge_import'):
            recommendations.append("Fix semantic framework import issues")

        # GoSpine integration recommendations
        gospine_checks = self.results['checks'].get('gospine_integration', {})
        failed_endpoints = [ep for ep, status in gospine_checks.items()
                           if isinstance(status, dict) and status.get('status_code') != 200]

        if failed_endpoints:
            recommendations.append(f"Fix GoSpine endpoints: {', '.join(failed_endpoints)}")

        self.results['recommendations'].extend(recommendations)
        logger.info(f"📋 Generated {len(recommendations)} recommendations")

    def _save_results(self):
        """Save check results to file."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"system_integrity_report_{timestamp}.json"
        filepath = os.path.join(os.path.dirname(__file__), 'logs', filename)

        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)

        logger.info(f"📄 Results saved to: {filepath}")

    def print_summary(self):
        """Print a summary of the check results."""
        print("\n" + "="*80)
        print("🔍 HVAC CRM SYSTEM INTEGRITY CHECK SUMMARY")
        print("="*80)

        # Overall status
        total_checks = len(self.results['checks'])
        print(f"📊 Total Checks Performed: {total_checks}")
        print(f"🔧 Fixes Applied: {len(self.results['fixes_applied'])}")
        print(f"💡 Recommendations: {len(self.results['recommendations'])}")

        # Container status
        containers = self.results['checks'].get('docker_containers', {})
        healthy_containers = sum(1 for status in containers.values()
                               if status.get('status') == 'running' and status.get('health') != 'unhealthy')
        print(f"🐳 Healthy Containers: {healthy_containers}/{len(containers)}")

        # Service status
        services = self.results['checks'].get('service_endpoints', {})
        available_services = sum(1 for status in services.values() if status.get('available'))
        print(f"🌐 Available Services: {available_services}/{len(services)}")

        # Key recommendations
        if self.results['recommendations']:
            print("\n🎯 KEY RECOMMENDATIONS:")
            for i, rec in enumerate(self.results['recommendations'][:5], 1):
                print(f"   {i}. {rec}")

        print("\n✅ System integrity check completed!")
        print("="*80)


async def main():
    """Main function to run system integrity check."""
    print("🚀 HVAC CRM System Integrity Checker & Rebuilder")
    print("=" * 60)

    checker = SystemIntegrityChecker()

    try:
        results = await checker.run_comprehensive_check()
        checker.print_summary()

        # Ask user if they want to see detailed results
        print(f"\n📄 Detailed results saved to logs/")
        print("🔍 Check the JSON report for complete analysis")

        return results

    except Exception as e:
        print(f"❌ System check failed: {e}")
        return None


if __name__ == "__main__":
    asyncio.run(main())
