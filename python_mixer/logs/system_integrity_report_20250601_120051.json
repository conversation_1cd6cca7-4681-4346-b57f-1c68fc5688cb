{"timestamp": "2025-06-01T12:00:50.274560", "checks": {"docker_containers": {"hvac-weaviate": {"status": "running", "health": "unknown", "created": "2025-05-31T21:53:10.421370998Z", "image": "semitechnologies/weaviate:1.25.0", "ports": {"50051/tcp": [{"HostIp": "0.0.0.0", "HostPort": "50051"}, {"HostIp": "::", "HostPort": "50051"}], "8080/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8082"}, {"HostIp": "::", "HostPort": "8082"}]}, "restart_count": 0}, "hvac-embeddings": {"status": "running", "health": "unknown", "created": "2025-05-31T21:53:10.113423257Z", "image": "cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2", "ports": {}, "restart_count": 0}, "hvac-redis": {"status": "running", "health": "unknown", "created": "2025-06-01T06:47:06.29115899Z", "image": "redis:7-alpine", "ports": {"6379/tcp": [{"HostIp": "0.0.0.0", "HostPort": "6379"}, {"HostIp": "::", "HostPort": "6379"}]}, "restart_count": 0}, "hvac-mongodb-local": {"status": "running", "health": "unknown", "created": "2025-05-30T12:24:23.707795212Z", "image": "mongo:7", "ports": {"27017/tcp": [{"HostIp": "0.0.0.0", "HostPort": "27017"}, {"HostIp": "::", "HostPort": "27017"}]}, "restart_count": 0}, "hvac-transcription-orchestrator": {"status": "not_found", "needs_rebuild": true}, "hvac-simple-stt": {"status": "running", "health": "unknown", "created": "2025-05-30T16:05:49.832189261Z", "image": "nvidia-stt-polish_simple-stt:latest", "ports": {"8889/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8889"}, {"HostIp": "::", "HostPort": "8889"}]}, "restart_count": 0}, "hvac-email-processor": {"status": "running", "health": "unknown", "created": "2025-05-30T15:53:47.062831631Z", "image": "nvidia-stt-polish_email-processor:latest", "ports": {}, "restart_count": 5209}, "hvac-audio-converter": {"status": "running", "health": "unknown", "created": "2025-05-30T14:59:27.235007915Z", "image": "nvidia-stt-polish_audio-converter:latest", "ports": {}, "restart_count": 5160}, "hvac-gemma-integration": {"status": "running", "health": "unknown", "created": "2025-05-30T14:59:27.234983862Z", "image": "nvidia-stt-polish_gemma-integration:latest", "ports": {}, "restart_count": 5249}}, "service_endpoints": {"weaviate": {"available": false, "response_time": 0.00186, "status_code": 404}, "gospine": {"available": false, "error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb340a8cb90>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "redis_local": {"available": false, "error": "No module named 'redis'"}, "redis_external": {"available": false, "error": "No module named 'redis'"}, "mongodb": {"available": true, "response_time": 0.001719, "status_code": 200}, "admin_interface": {"available": false, "error": "HTTPConnectionPool(host='localhost', port=7861): Max retries exceeded with url: /health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb340a8d210>: Failed to establish a new connection: [Errno 111] Connection refused'))"}}, "weaviate_health": {"/v1/.well-known/live": {"status_code": 200, "response_time": 0.00123, "content_length": 0}, "/v1/.well-known/ready": {"status_code": 200, "response_time": 0.000746, "content_length": 0}, "/v1/meta": {"status_code": 200, "response_time": 0.005361, "content_length": 1894, "meta_data": {"hostname": "http://[::]:8080", "modules": {"backup-filesystem": {"backupsPath": "/var/lib/weaviate/backups"}, "text2vec-transformers": {"model": {"_attn_implementation_autoset": false, "_name_or_path": "sentence-transformers/all-mpnet-base-v2", "add_cross_attention": false, "architectures": ["MPNetForMaskedLM"], "attention_probs_dropout_prob": 0.1, "bad_words_ids": null, "begin_suppress_tokens": null, "bos_token_id": 0, "chunk_size_feed_forward": 0, "cross_attention_hidden_size": null, "decoder_start_token_id": null, "diversity_penalty": 0, "do_sample": false, "early_stopping": false, "encoder_no_repeat_ngram_size": 0, "eos_token_id": 2, "exponential_decay_length_penalty": null, "finetuning_task": null, "forced_bos_token_id": null, "forced_eos_token_id": null, "hidden_act": "gelu", "hidden_dropout_prob": 0.1, "hidden_size": 768, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "initializer_range": 0.02, "intermediate_size": 3072, "is_decoder": false, "is_encoder_decoder": false, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "layer_norm_eps": 1e-05, "length_penalty": 1, "max_length": 20, "max_position_embeddings": 514, "min_length": 0, "model_type": "mpnet", "no_repeat_ngram_size": 0, "num_attention_heads": 12, "num_beam_groups": 1, "num_beams": 1, "num_hidden_layers": 12, "num_return_sequences": 1, "output_attentions": false, "output_hidden_states": false, "output_scores": false, "pad_token_id": 1, "prefix": null, "problem_type": null, "pruned_heads": {}, "relative_attention_num_buckets": 32, "remove_invalid_values": false, "repetition_penalty": 1, "return_dict": true, "return_dict_in_generate": false, "sep_token_id": null, "suppress_tokens": null, "task_specific_params": null, "temperature": 1, "tf_legacy_loss": false, "tie_encoder_decoder": false, "tie_word_embeddings": true, "tokenizer_class": null, "top_k": 50, "top_p": 1, "torch_dtype": null, "torchscript": false, "transformers_version": "4.48.2", "typical_p": 1, "use_bfloat16": false, "vocab_size": 30527}, "model_path": "./models/model"}}, "version": "1.25.0"}}, "schema_classes": 1, "client_connection": true, "client_version": "v4", "ready_status": true}, "gospine_integration": {"/api/v1/health": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/health (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb340a8c390>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/pipeline/stages": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/pipeline/stages (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb33d3b6b90>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/scoring/criteria": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/scoring/criteria (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb33d3b4b50>: Failed to establish a new connection: [Errno 111] Connection refused'))"}, "/api/v1/knowledge/suggestions?context=test": {"error": "HTTPConnectionPool(host='localhost', port=8091): Max retries exceeded with url: /api/v1/knowledge/suggestions?context=test (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fb33d3a2850>: Failed to establish a new connection: [Errno 111] Connection refused'))"}}, "system_resources": {"cpu_percent": 0.3, "memory": {"total": 42020954112, "available": 27983413248, "percent": 33.4}, "disk": {"total": 1081101176832, "free": 872386551808, "percent": 15.0}, "network": {"bytes_sent": 40975139058, "bytes_recv": 38308153207, "packets_sent": 2773047, "packets_recv": 3369652, "errin": 0, "errout": 0, "dropin": 0, "dropout": 0}, "processes": 223}, "semantic_framework": {"bridge_import": true, "bridge_creation": true, "semantic_analysis": {"working": true, "processing_time": 4e-06, "confidence": 0.0}}}, "fixes_applied": ["Container hvac-transcription-orchestrator not found - needs creation", "Rebuilt container: hvac-transcription-orchestrator"], "recommendations": ["Restore services: weaviate, gospine, redis_local, redis_external, admin_interface", "Fix GoSpine endpoints: /api/v1/health, /api/v1/pipeline/stages, /api/v1/scoring/criteria, /api/v1/knowledge/suggestions?context=test"]}