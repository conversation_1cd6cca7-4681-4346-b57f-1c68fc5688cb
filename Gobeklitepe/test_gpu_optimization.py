#!/usr/bin/env python3
"""
🧪 GPU Optimization Testing and Validation Script
=================================================

Comprehensive testing script for GPU-optimized HVAC CRM system:
- GPU utilization verification
- Memory allocation testing
- Agent performance benchmarking
- LangSmith integration validation
- System stability testing

Author: HVAC CRM Team
Date: 2025-06-01
"""

import asyncio
import logging
import subprocess
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Optional
import statistics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class GPUOptimizationTester:
    """Comprehensive tester for GPU-optimized HVAC CRM system."""
    
    def __init__(self):
        """Initialize the tester."""
        self.test_results = {}
        self.performance_metrics = {}
        self.endpoints = {
            'weaviate': 'http://localhost:8082',
            'embeddings': 'http://localhost:8080',
            'langsmith_monitor': 'http://localhost:8090',
            'gpu_monitor': 'http://localhost:8091'
        }
    
    def get_gpu_stats(self) -> Dict:
        """Get current GPU statistics."""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                memory_used, memory_total, gpu_util, temp = result.stdout.strip().split(', ')
                return {
                    'memory_used_mb': int(memory_used),
                    'memory_total_mb': int(memory_total),
                    'memory_usage_percent': round((int(memory_used) / int(memory_total)) * 100, 1),
                    'gpu_utilization': int(gpu_util),
                    'temperature': int(temp)
                }
        except Exception as e:
            logger.error(f"Error getting GPU stats: {e}")
        
        return {}
    
    async def test_gpu_availability(self) -> bool:
        """Test 1: GPU Availability and Basic Stats."""
        logger.info("🧪 Test 1: GPU Availability")
        
        gpu_stats = self.get_gpu_stats()
        if gpu_stats:
            self.test_results['gpu_availability'] = {
                'status': 'PASS',
                'details': gpu_stats,
                'message': f"GPU available with {gpu_stats['memory_total_mb']}MB VRAM"
            }
            logger.info(f"✅ GPU Test PASSED: {gpu_stats['memory_total_mb']}MB VRAM available")
            return True
        else:
            self.test_results['gpu_availability'] = {
                'status': 'FAIL',
                'message': 'GPU not available or nvidia-smi failed'
            }
            logger.error("❌ GPU Test FAILED")
            return False
    
    async def test_service_connectivity(self) -> bool:
        """Test 2: Service Connectivity."""
        logger.info("🧪 Test 2: Service Connectivity")
        
        connectivity_results = {}
        all_connected = True
        
        for service_name, url in self.endpoints.items():
            try:
                response = requests.get(f"{url}/health", timeout=5)
                if response.status_code == 200:
                    connectivity_results[service_name] = "CONNECTED"
                else:
                    # Try alternative endpoints
                    response = requests.get(url, timeout=5)
                    if response.status_code in [200, 204]:
                        connectivity_results[service_name] = "CONNECTED"
                    else:
                        connectivity_results[service_name] = "FAILED"
                        all_connected = False
            except Exception as e:
                connectivity_results[service_name] = f"FAILED: {str(e)}"
                all_connected = False
        
        self.test_results['service_connectivity'] = {
            'status': 'PASS' if all_connected else 'PARTIAL',
            'details': connectivity_results
        }
        
        if all_connected:
            logger.info("✅ Service Connectivity Test PASSED")
        else:
            logger.warning("⚠️ Service Connectivity Test PARTIAL")
        
        return all_connected
    
    async def test_embeddings_performance(self) -> bool:
        """Test 3: Embeddings Performance with GPU."""
        logger.info("🧪 Test 3: Embeddings Performance")
        
        test_texts = [
            "HVAC system maintenance required for LG air conditioner",
            "Daikin heat pump installation in Warsaw office building",
            "Emergency repair needed for Mitsubishi cooling system",
            "Annual service inspection for commercial HVAC equipment",
            "Energy efficiency optimization for building climate control"
        ]
        
        performance_times = []
        gpu_utilization_before = self.get_gpu_stats().get('gpu_utilization', 0)
        
        try:
            for i, text in enumerate(test_texts):
                start_time = time.time()
                
                # Test embeddings endpoint
                response = requests.post(
                    f"{self.endpoints['embeddings']}/embed",
                    json={"text": text},
                    timeout=30
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                performance_times.append(processing_time)
                
                logger.info(f"   Text {i+1}: {processing_time:.2f}s")
                
                if response.status_code != 200:
                    logger.warning(f"   Warning: Response code {response.status_code}")
        
        except Exception as e:
            logger.error(f"❌ Embeddings test failed: {e}")
            self.test_results['embeddings_performance'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            return False
        
        gpu_utilization_after = self.get_gpu_stats().get('gpu_utilization', 0)
        
        avg_time = statistics.mean(performance_times) if performance_times else 0
        max_time = max(performance_times) if performance_times else 0
        min_time = min(performance_times) if performance_times else 0
        
        self.test_results['embeddings_performance'] = {
            'status': 'PASS' if avg_time < 5.0 else 'SLOW',
            'avg_processing_time': round(avg_time, 2),
            'max_processing_time': round(max_time, 2),
            'min_processing_time': round(min_time, 2),
            'gpu_utilization_increase': gpu_utilization_after - gpu_utilization_before,
            'total_tests': len(test_texts)
        }
        
        if avg_time < 5.0:
            logger.info(f"✅ Embeddings Performance Test PASSED: {avg_time:.2f}s average")
        else:
            logger.warning(f"⚠️ Embeddings Performance Test SLOW: {avg_time:.2f}s average")
        
        return True
    
    async def test_memory_allocation(self) -> bool:
        """Test 4: Memory Allocation and Usage."""
        logger.info("🧪 Test 4: Memory Allocation")
        
        gpu_stats_before = self.get_gpu_stats()
        
        # Simulate heavy workload
        try:
            # Test multiple concurrent embeddings
            tasks = []
            for i in range(5):
                task = requests.post(
                    f"{self.endpoints['embeddings']}/embed",
                    json={"text": f"Concurrent test {i} for HVAC system analysis"},
                    timeout=30
                )
                tasks.append(task)
            
            # Wait a moment for processing
            await asyncio.sleep(2)
            
            gpu_stats_during = self.get_gpu_stats()
            
            memory_increase = gpu_stats_during['memory_used_mb'] - gpu_stats_before['memory_used_mb']
            
            self.test_results['memory_allocation'] = {
                'status': 'PASS' if memory_increase < 4000 else 'HIGH_USAGE',  # Less than 4GB increase
                'memory_before_mb': gpu_stats_before['memory_used_mb'],
                'memory_during_mb': gpu_stats_during['memory_used_mb'],
                'memory_increase_mb': memory_increase,
                'memory_increase_gb': round(memory_increase / 1024, 2)
            }
            
            if memory_increase < 4000:
                logger.info(f"✅ Memory Allocation Test PASSED: {memory_increase}MB increase")
            else:
                logger.warning(f"⚠️ Memory Allocation Test HIGH_USAGE: {memory_increase}MB increase")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Memory allocation test failed: {e}")
            self.test_results['memory_allocation'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            return False
    
    async def test_langsmith_integration(self) -> bool:
        """Test 5: LangSmith Integration."""
        logger.info("🧪 Test 5: LangSmith Integration")
        
        try:
            # Test LangSmith monitoring endpoint
            response = requests.get(f"{self.endpoints['langsmith_monitor']}", timeout=10)
            
            if response.status_code == 200:
                self.test_results['langsmith_integration'] = {
                    'status': 'PASS',
                    'message': 'LangSmith monitoring interface accessible'
                }
                logger.info("✅ LangSmith Integration Test PASSED")
                return True
            else:
                self.test_results['langsmith_integration'] = {
                    'status': 'FAIL',
                    'message': f'LangSmith interface returned {response.status_code}'
                }
                logger.error("❌ LangSmith Integration Test FAILED")
                return False
                
        except Exception as e:
            self.test_results['langsmith_integration'] = {
                'status': 'FAIL',
                'error': str(e)
            }
            logger.error(f"❌ LangSmith Integration Test FAILED: {e}")
            return False
    
    def generate_test_report(self) -> str:
        """Generate comprehensive test report."""
        report = {
            'test_timestamp': datetime.now().isoformat(),
            'test_results': self.test_results,
            'final_gpu_stats': self.get_gpu_stats(),
            'summary': {
                'total_tests': len(self.test_results),
                'passed_tests': len([r for r in self.test_results.values() if r['status'] == 'PASS']),
                'failed_tests': len([r for r in self.test_results.values() if r['status'] == 'FAIL']),
                'warnings': len([r for r in self.test_results.values() if r['status'] in ['PARTIAL', 'SLOW', 'HIGH_USAGE']])
            }
        }
        
        return json.dumps(report, indent=2)
    
    async def run_all_tests(self) -> bool:
        """Run all GPU optimization tests."""
        logger.info("🚀 Starting GPU Optimization Test Suite")
        logger.info("="*60)
        
        tests = [
            self.test_gpu_availability,
            self.test_service_connectivity,
            self.test_embeddings_performance,
            self.test_memory_allocation,
            self.test_langsmith_integration
        ]
        
        all_passed = True
        for test in tests:
            try:
                result = await test()
                if not result:
                    all_passed = False
                await asyncio.sleep(1)  # Brief pause between tests
            except Exception as e:
                logger.error(f"❌ Test failed with exception: {e}")
                all_passed = False
        
        # Generate and save report
        report = self.generate_test_report()
        
        with open('gpu_optimization_test_report.json', 'w') as f:
            f.write(report)
        
        logger.info("="*60)
        logger.info("📊 Test Summary:")
        summary = json.loads(report)['summary']
        logger.info(f"   Total Tests: {summary['total_tests']}")
        logger.info(f"   Passed: {summary['passed_tests']}")
        logger.info(f"   Failed: {summary['failed_tests']}")
        logger.info(f"   Warnings: {summary['warnings']}")
        
        if all_passed:
            logger.info("🎉 All tests completed successfully!")
        else:
            logger.warning("⚠️ Some tests failed or have warnings. Check the report.")
        
        logger.info("📄 Detailed report saved to: gpu_optimization_test_report.json")
        
        return all_passed


async def main():
    """Main entry point."""
    tester = GPUOptimizationTester()
    success = await tester.run_all_tests()
    
    if success:
        print("\n✅ GPU optimization validation completed successfully!")
    else:
        print("\n⚠️ GPU optimization validation completed with issues.")
    
    return success


if __name__ == "__main__":
    asyncio.run(main())
