version: '3.8'

services:
  # Weaviate Core Database
  weaviate:
    image: semitechnologies/weaviate:1.25.0
    container_name: hvac-weaviate
    ports:
      - "8082:8080"  # Changed to avoid conflicts
      - "50051:50051"  # gRPC port
    environment:
      # Basic Configuration
      QUERY_DEFAULTS_LIMIT: 25
      AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED: 'true'
      PERSISTENCE_DATA_PATH: '/var/lib/weaviate'
      
      # Vectorizer Configuration
      DEFAULT_VECTORIZER_MODULE: 'text2vec-transformers'
      ENABLE_MODULES: 'text2vec-transformers,backup-filesystem'
      TRANSFORMERS_INFERENCE_API: 'http://t2v-transformers:8080'
      
      # Resource Management (Optimized for WSL with 40GB RAM, 4 cores max)
      LIMIT_RESOURCES: 'true'
      GOMEMLIMIT: '12884901888'  # 12GB in bytes (conservative for 40GB total)
      GOMAXPROCS: '4'     # Use 4 cores as specified
      
      # Performance Optimization
      QUERY_MAXIMUM_RESULTS: 10000
      TRACK_VECTOR_DIMENSIONS: 'true'
      
      # Cluster Configuration
      CLUSTER_HOSTNAME: 'node1'
      CLUSTER_GOSSIP_BIND_PORT: '7100'
      CLUSTER_DATA_BIND_PORT: '7101'
      
      # Backup Configuration
      BACKUP_FILESYSTEM_PATH: '/var/lib/weaviate/backups'
      
      # Monitoring
      PROMETHEUS_MONITORING_ENABLED: 'true'
      PROMETHEUS_MONITORING_PORT: '2112'
      
    volumes:
      - ./weaviate_data:/var/lib/weaviate
      - ./weaviate_backups:/var/lib/weaviate/backups
    restart: on-failure:3
    depends_on:
      - t2v-transformers
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/v1/.well-known/ready"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 14GB  # Hard limit slightly above GOMEMLIMIT (12GB)
          cpus: '4.0'   # Limit to 4 CPU cores
        reservations:
          memory: 10GB
          cpus: '2.0'
    networks:
      - weaviate-network

  # GPU-Optimized Embedding Model for HVAC CRM
  t2v-transformers:
    image: cr.weaviate.io/semitechnologies/transformers-inference:sentence-transformers-all-mpnet-base-v2
    container_name: hvac-embeddings
    environment:
      ENABLE_CUDA: '1'  # Enable GPU acceleration
      CUDA_VISIBLE_DEVICES: '0'  # Use GPU 0

      # Model Configuration
      MODEL_NAME: 'sentence-transformers/all-mpnet-base-v2'
      MAX_SEQUENCE_LENGTH: '512'
      BATCH_SIZE: '32'  # Increased batch size for GPU

      # GPU Performance Tuning
      WORKERS: '4'  # Increased workers for GPU
      TIMEOUT: '60'  # Reduced timeout for GPU processing

      # Memory Management (Conservative allocation for 12GB VRAM)
      PYTORCH_CUDA_ALLOC_CONF: 'max_split_size_mb:2048'
      CUDA_MEMORY_FRACTION: '0.3'  # Use max 30% of VRAM (~3.6GB)
    restart: on-failure:3
    devices:
      - /dev/nvidia0:/dev/nvidia0
      - /dev/nvidiactl:/dev/nvidiactl
      - /dev/nvidia-uvm:/dev/nvidia-uvm
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 4GB  # Memory limit for container
    healthcheck:
      test: ["CMD", "python3", "-c", "import urllib.request; urllib.request.urlopen('http://localhost:8080/.well-known/ready')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 180s  # Longer startup for GPU model loading
    networks:
      - weaviate-network



  # LangSmith Agent Monitoring for HVAC CRM
  langsmith-monitor:
    image: python:3.11-slim
    container_name: hvac-langsmith-monitor
    environment:
      # LangSmith Configuration
      LANGCHAIN_TRACING_V2: 'true'
      LANGCHAIN_API_KEY: '${LANGSMITH_API_KEY:-demo_key}'
      LANGCHAIN_PROJECT: 'HVAC-CRM-Agents'
      LANGCHAIN_ENDPOINT: 'https://api.smith.langchain.com'

      # HVAC Agent Configuration
      HVAC_AGENTS_ENABLED: 'conversational,analytical,decision_making,integration,optimization'
      MONITORING_INTERVAL: '30'
      LOG_LEVEL: 'INFO'

      # Integration with existing services
      WEAVIATE_URL: 'http://weaviate:8080'
      EMBEDDINGS_URL: 'http://t2v-transformers:8080'
      GEMMA_4B_URL: 'http://*************:1234'
      BIELIK_V3_URL: 'http://host.docker.internal:8877'
      GEMMA3_4B_URL: 'http://host.docker.internal:8878'
      GEMMA_3_4B_IT_URL: 'http://host.docker.internal:8879'
    ports:
      - "8090:8090"  # LangSmith monitoring dashboard
    volumes:
      - ./langsmith_config:/app/config
      - ./langsmith_logs:/app/logs
      - ./langsmith_data:/app/data
    networks:
      - weaviate-network
    restart: unless-stopped
    command: >
      bash -c "
        pip install langsmith langchain langchain-community gradio pandas plotly &&
        python -c '
        import os
        import time
        import json
        import logging
        from datetime import datetime
        from langsmith import Client
        import gradio as gr

        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)

        # Initialize LangSmith client
        client = Client(
            api_url=os.getenv(\"LANGCHAIN_ENDPOINT\"),
            api_key=os.getenv(\"LANGCHAIN_API_KEY\")
        )

        def get_agent_metrics():
            \"\"\"Get metrics for all HVAC agents\"\"\"
            try:
                runs = client.list_runs(
                    project_name=os.getenv(\"LANGCHAIN_PROJECT\"),
                    limit=100
                )

                metrics = {
                    \"total_runs\": len(list(runs)),
                    \"success_rate\": 0.95,  # Placeholder
                    \"avg_response_time\": 1.2,  # Placeholder
                    \"token_usage\": 15000,  # Placeholder
                    \"last_updated\": datetime.now().isoformat()
                }

                return json.dumps(metrics, indent=2)
            except Exception as e:
                return f\"Error: {str(e)}\"

        def create_monitoring_interface():
            \"\"\"Create Gradio interface for LangSmith monitoring\"\"\"
            with gr.Blocks(title=\"HVAC CRM - LangSmith Agent Monitoring\") as interface:
                gr.Markdown(\"# 🤖 HVAC CRM - LangSmith Agent Monitoring\")
                gr.Markdown(\"Real-time monitoring and analytics for HVAC AI agents\")

                with gr.Tabs():
                    with gr.Tab(\"📊 Agent Metrics\"):
                        metrics_output = gr.Code(
                            value=get_agent_metrics(),
                            language=\"json\",
                            label=\"Agent Performance Metrics\"
                        )
                        refresh_btn = gr.Button(\"🔄 Refresh Metrics\")
                        refresh_btn.click(get_agent_metrics, outputs=metrics_output)

                    with gr.Tab(\"🔍 Agent Traces\"):
                        gr.Markdown(\"### Recent Agent Executions\")
                        traces_output = gr.Textbox(
                            value=\"Traces will appear here...\",
                            lines=10,
                            label=\"Agent Execution Traces\"
                        )

                    with gr.Tab(\"⚙️ Configuration\"):
                        gr.Markdown(\"### LangSmith Configuration\")
                        config_display = gr.Code(
                            value=json.dumps({
                                \"project\": os.getenv(\"LANGCHAIN_PROJECT\"),
                                \"agents_enabled\": os.getenv(\"HVAC_AGENTS_ENABLED\").split(\",\"),
                                \"monitoring_interval\": os.getenv(\"MONITORING_INTERVAL\"),
                                \"endpoints\": {
                                    \"weaviate\": os.getenv(\"WEAVIATE_URL\"),
                                    \"embeddings\": os.getenv(\"EMBEDDINGS_URL\"),
                                    \"gemma_4b\": os.getenv(\"GEMMA_4B_URL\"),
                                    \"bielik_v3\": os.getenv(\"BIELIK_V3_URL\")
                                }
                            }, indent=2),
                            language=\"json\",
                            label=\"Current Configuration\"
                        )

            return interface

        # Launch monitoring interface
        interface = create_monitoring_interface()
        interface.launch(
            server_name=\"0.0.0.0\",
            server_port=8090,
            share=False
        )
        '"

  # GPU Memory Monitoring for HVAC CRM
  gpu-monitor:
    image: nvidia/cuda:12.0-runtime-ubuntu20.04
    container_name: hvac-gpu-monitor
    environment:
      NVIDIA_VISIBLE_DEVICES: all
      CUDA_VISIBLE_DEVICES: '0'
    devices:
      - /dev/nvidia0:/dev/nvidia0
      - /dev/nvidiactl:/dev/nvidiactl
      - /dev/nvidia-uvm:/dev/nvidia-uvm
    ports:
      - "8091:8091"  # GPU monitoring dashboard
    volumes:
      - ./gpu_monitoring:/app/monitoring
    networks:
      - weaviate-network
    restart: unless-stopped
    command: >
      bash -c "
        apt-get update && apt-get install -y python3 python3-pip curl &&
        pip3 install gpustat psutil gradio pandas plotly &&
        python3 -c '
        import os
        import time
        import json
        import subprocess
        import gradio as gr
        from datetime import datetime

        def get_gpu_stats():
            \"\"\"Get current GPU statistics\"\"\"
            try:
                result = subprocess.run([\"nvidia-smi\", \"--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu\", \"--format=csv,noheader,nounits\"],
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    memory_used, memory_total, gpu_util, temp = result.stdout.strip().split(\", \")

                    stats = {
                        \"memory_used_mb\": int(memory_used),
                        \"memory_total_mb\": int(memory_total),
                        \"memory_used_gb\": round(int(memory_used) / 1024, 2),
                        \"memory_total_gb\": round(int(memory_total) / 1024, 2),
                        \"memory_usage_percent\": round((int(memory_used) / int(memory_total)) * 100, 1),
                        \"gpu_utilization_percent\": int(gpu_util),
                        \"temperature_celsius\": int(temp),
                        \"available_memory_gb\": round((int(memory_total) - int(memory_used)) / 1024, 2),
                        \"timestamp\": datetime.now().isoformat()
                    }

                    return json.dumps(stats, indent=2)
                else:
                    return \"Error: Could not get GPU stats\"
            except Exception as e:
                return f\"Error: {str(e)}\"

        def get_memory_allocation():
            \"\"\"Get memory allocation breakdown\"\"\"
            allocation = {
                \"embeddings_container\": \"~3.6GB (30% VRAM)\",
                \"weaviate_operations\": \"~2.0GB (17% VRAM)\",
                \"gemma_4b_model\": \"~4.0GB (33% VRAM)\",
                \"system_buffer\": \"~2.4GB (20% VRAM)\",
                \"total_allocated\": \"12.0GB (100% VRAM)\"
            }
            return json.dumps(allocation, indent=2)

        def create_gpu_monitoring_interface():
            \"\"\"Create GPU monitoring interface\"\"\"
            with gr.Blocks(title=\"HVAC CRM - GPU Memory Monitor\") as interface:
                gr.Markdown(\"# 🎮 HVAC CRM - GPU Memory Monitor\")
                gr.Markdown(\"Real-time GPU memory and utilization monitoring for HVAC AI models\")

                with gr.Tabs():
                    with gr.Tab(\"📊 GPU Statistics\"):
                        gpu_stats = gr.Code(
                            value=get_gpu_stats(),
                            language=\"json\",
                            label=\"Current GPU Statistics\"
                        )
                        refresh_gpu_btn = gr.Button(\"🔄 Refresh GPU Stats\")
                        refresh_gpu_btn.click(get_gpu_stats, outputs=gpu_stats)

                    with gr.Tab(\"💾 Memory Allocation\"):
                        memory_allocation = gr.Code(
                            value=get_memory_allocation(),
                            language=\"json\",
                            label=\"VRAM Allocation Plan\"
                        )

                        gr.Markdown(\"### Memory Usage Guidelines\")
                        gr.Markdown(\"\"\"
                        - **Embeddings Container**: 30% VRAM for sentence-transformers
                        - **Weaviate Operations**: 17% VRAM for vector operations
                        - **Gemma-4B Model**: 33% VRAM for language model
                        - **System Buffer**: 20% VRAM for overhead and safety
                        \"\"\")

                    with gr.Tab(\"⚠️ Alerts\"):
                        gr.Markdown(\"### GPU Memory Alerts\")
                        alerts_output = gr.Textbox(
                            value=\"No alerts - GPU memory usage within normal parameters\",
                            lines=5,
                            label=\"Memory Alerts\"
                        )

            return interface

        # Launch GPU monitoring interface
        interface = create_gpu_monitoring_interface()
        interface.launch(
            server_name=\"0.0.0.0\",
            server_port=8091,
            share=False
        )
        '"

  # Monitoring and Metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Metrics Visualization
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    restart: unless-stopped
    profiles:
      - monitoring
    networks:
      - weaviate-network

  # Redis Configuration - Using External Redis Server
  # Redis service removed - using external Redis at **************:3037
  # Configure applications to connect to: **************:3037

  # Backup Service
  backup-service:
    image: alpine:latest
    container_name: hvac-backup
    volumes:
      - ./weaviate_data:/source:ro
      - ./backups:/backup
      - ./scripts/backup.sh:/backup.sh:ro
    command: |
      sh -c "
        apk add --no-cache curl &&
        chmod +x /backup.sh &&
        crond -f
      "
    environment:
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - WEAVIATE_URL=http://weaviate:8080
      - BACKUP_RETENTION_DAYS=30
    restart: unless-stopped
    profiles:
      - backup
    networks:
      - weaviate-network

volumes:
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  weaviate-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
