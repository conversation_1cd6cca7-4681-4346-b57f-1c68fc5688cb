# 🚀 HVAC CRM - GPU Optimized Environment Configuration
# ================================================================

# LangSmith Configuration for Agent Monitoring
LANGSMITH_API_KEY=your_langsmith_api_key_here
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=HVAC-CRM-Agents
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com

# GPU Memory Management (12GB VRAM Total)
CUDA_VISIBLE_DEVICES=0
PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:2048
CUDA_MEMORY_FRACTION=0.3

# HVAC AI Model Endpoints
GEMMA_4B_URL=http://*************:1234
BIELIK_V3_URL=http://localhost:8877
GEMMA3_4B_URL=http://localhost:8878
GEMMA_3_4B_IT_URL=http://localhost:8879

# Weaviate Configuration
WEAVIATE_URL=http://localhost:8082
WEAVIATE_GRPC_PORT=50051
ENABLE_MODULES=text2vec-transformers,backup-filesystem

# Embeddings Configuration
EMBEDDINGS_URL=http://localhost:8080
ENABLE_CUDA=1
BATCH_SIZE=32
MAX_SEQUENCE_LENGTH=512
WORKERS=4

# HVAC Agent Configuration
HVAC_AGENTS_ENABLED=conversational,analytical,decision_making,integration,optimization
MONITORING_INTERVAL=30
LOG_LEVEL=INFO

# Performance Monitoring
GPU_MONITORING_PORT=8091
LANGSMITH_MONITORING_PORT=8090
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Memory Allocation Strategy
EMBEDDINGS_MEMORY_LIMIT=4GB
WEAVIATE_MEMORY_LIMIT=14GB
GPU_MONITOR_MEMORY_LIMIT=1GB
LANGSMITH_MEMORY_LIMIT=2GB

# Security and Access
AUTHENTICATION_ANONYMOUS_ACCESS_ENABLED=true
BACKUP_FILESYSTEM_PATH=/var/lib/weaviate/backups
PROMETHEUS_MONITORING_ENABLED=true

# Development and Debugging
DEBUG_MODE=false
VERBOSE_LOGGING=true
ENABLE_PROFILING=false
