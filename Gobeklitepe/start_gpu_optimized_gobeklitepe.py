#!/usr/bin/env python3
"""
🚀 GPU-Optimized Gobeklitepe HVAC CRM System Launcher
====================================================

Advanced launcher for GPU-optimized HVAC CRM system with:
- NVIDIA GPU acceleration for embeddings and vector operations
- LangSmith agent monitoring and tracing
- Memory management for 12GB VRAM
- Integration with Gemma-4B and existing AI stack
- Real-time performance monitoring

Author: HVAC CRM Team
Date: 2025-06-01
"""

import asyncio
import logging
import os
import sys
import subprocess
import time
import json
import requests
from datetime import datetime
from typing import Dict, List, Optional
import psutil

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/gpu_optimized_launcher.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GPUOptimizedGobeklitepeLauncher:
    """GPU-optimized launcher for Gobeklitepe HVAC CRM system."""
    
    def __init__(self):
        """Initialize the launcher."""
        self.services_status = {}
        self.gpu_stats = {}
        self.memory_allocation = {
            'embeddings': {'allocated': '3.6GB', 'percentage': 30},
            'weaviate': {'allocated': '2.0GB', 'percentage': 17},
            'gemma_4b': {'allocated': '4.0GB', 'percentage': 33},
            'buffer': {'allocated': '2.4GB', 'percentage': 20}
        }
        
        # Service endpoints
        self.endpoints = {
            'weaviate': 'http://localhost:8082',
            'embeddings': 'http://localhost:8080',
            'langsmith_monitor': 'http://localhost:8090',
            'gpu_monitor': 'http://localhost:8091',
            'gemma_4b': 'http://*************:1234',
            'bielik_v3': 'http://localhost:8877',
            'gemma3_4b': 'http://localhost:8878',
            'gemma_3_4b_it': 'http://localhost:8879'
        }
    
    def check_gpu_availability(self) -> bool:
        """Check if NVIDIA GPU is available and get stats."""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total,utilization.gpu,temperature.gpu', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                memory_used, memory_total, gpu_util, temp = result.stdout.strip().split(', ')
                
                self.gpu_stats = {
                    'memory_used_mb': int(memory_used),
                    'memory_total_mb': int(memory_total),
                    'memory_available_mb': int(memory_total) - int(memory_used),
                    'gpu_utilization': int(gpu_util),
                    'temperature': int(temp),
                    'memory_usage_percent': round((int(memory_used) / int(memory_total)) * 100, 1)
                }
                
                logger.info(f"✅ GPU Available: {self.gpu_stats['memory_available_mb']}MB free of {self.gpu_stats['memory_total_mb']}MB")
                return True
            else:
                logger.error("❌ NVIDIA GPU not available")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error checking GPU: {e}")
            return False
    
    def check_memory_requirements(self) -> bool:
        """Check if system has enough memory for all services."""
        total_required_gb = sum([float(alloc['allocated'].replace('GB', '')) 
                               for alloc in self.memory_allocation.values()])
        
        available_gb = self.gpu_stats['memory_available_mb'] / 1024
        
        if available_gb >= total_required_gb:
            logger.info(f"✅ Memory check passed: {available_gb:.1f}GB available, {total_required_gb:.1f}GB required")
            return True
        else:
            logger.warning(f"⚠️ Memory warning: {available_gb:.1f}GB available, {total_required_gb:.1f}GB required")
            return False
    
    async def check_service_health(self, service_name: str, url: str) -> bool:
        """Check if a service is healthy."""
        try:
            response = requests.get(f"{url}/health", timeout=5)
            if response.status_code == 200:
                self.services_status[service_name] = "✅ Healthy"
                return True
        except:
            pass
        
        try:
            response = requests.get(url, timeout=5)
            if response.status_code in [200, 204]:
                self.services_status[service_name] = "✅ Running"
                return True
        except:
            pass
        
        self.services_status[service_name] = "❌ Offline"
        return False
    
    async def start_docker_services(self) -> bool:
        """Start Docker services with GPU optimization."""
        try:
            logger.info("🚀 Starting GPU-optimized Docker services...")
            
            # Stop existing services
            subprocess.run(['docker-compose', '-f', 'docker-compose.weaviate.yml', 'down'], 
                         capture_output=True)
            
            # Start services with GPU support
            result = subprocess.run(['docker-compose', '-f', 'docker-compose.weaviate.yml', 'up', '-d'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✅ Docker services started successfully")
                return True
            else:
                logger.error(f"❌ Failed to start Docker services: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting Docker services: {e}")
            return False
    
    async def wait_for_services(self, timeout: int = 300) -> bool:
        """Wait for all services to become healthy."""
        logger.info("⏳ Waiting for services to become healthy...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            all_healthy = True
            
            for service_name, url in self.endpoints.items():
                if service_name in ['gemma_4b', 'bielik_v3', 'gemma3_4b', 'gemma_3_4b_it']:
                    # External services - just check if reachable
                    continue
                
                is_healthy = await self.check_service_health(service_name, url)
                if not is_healthy:
                    all_healthy = False
            
            if all_healthy:
                logger.info("✅ All services are healthy!")
                return True
            
            await asyncio.sleep(10)
        
        logger.error("❌ Timeout waiting for services to become healthy")
        return False
    
    def display_system_status(self):
        """Display comprehensive system status."""
        print("\n" + "="*80)
        print("🚀 HVAC CRM - GPU-Optimized Gobeklitepe System Status")
        print("="*80)
        
        # GPU Status
        print(f"\n🎮 GPU Status:")
        print(f"   Memory Used: {self.gpu_stats['memory_used_mb']}MB ({self.gpu_stats['memory_usage_percent']}%)")
        print(f"   Memory Available: {self.gpu_stats['memory_available_mb']}MB")
        print(f"   GPU Utilization: {self.gpu_stats['gpu_utilization']}%")
        print(f"   Temperature: {self.gpu_stats['temperature']}°C")
        
        # Memory Allocation
        print(f"\n💾 VRAM Allocation Plan:")
        for component, allocation in self.memory_allocation.items():
            print(f"   {component.title()}: {allocation['allocated']} ({allocation['percentage']}%)")
        
        # Service Status
        print(f"\n🔧 Service Status:")
        for service, status in self.services_status.items():
            print(f"   {service}: {status}")
        
        # Access URLs
        print(f"\n🌐 Access URLs:")
        print(f"   Weaviate API: {self.endpoints['weaviate']}")
        print(f"   LangSmith Monitor: {self.endpoints['langsmith_monitor']}")
        print(f"   GPU Monitor: {self.endpoints['gpu_monitor']}")
        print(f"   Embeddings API: {self.endpoints['embeddings']}")
        
        print("="*80)
    
    async def run(self):
        """Main launcher routine."""
        logger.info("🚀 Starting GPU-Optimized Gobeklitepe HVAC CRM System...")
        
        # Check GPU availability
        if not self.check_gpu_availability():
            logger.error("❌ GPU not available. Exiting...")
            return False
        
        # Check memory requirements
        if not self.check_memory_requirements():
            logger.warning("⚠️ Memory requirements not fully met. Proceeding with caution...")
        
        # Start Docker services
        if not await self.start_docker_services():
            logger.error("❌ Failed to start Docker services. Exiting...")
            return False
        
        # Wait for services to become healthy
        if not await self.wait_for_services():
            logger.error("❌ Services failed to become healthy. Check logs...")
            return False
        
        # Display system status
        self.display_system_status()
        
        logger.info("🎉 GPU-Optimized Gobeklitepe HVAC CRM System is ready!")
        logger.info("📊 Monitor GPU usage at: http://localhost:8091")
        logger.info("🤖 Monitor agents at: http://localhost:8090")
        
        return True


async def main():
    """Main entry point."""
    launcher = GPUOptimizedGobeklitepeLauncher()
    success = await launcher.run()
    
    if success:
        print("\n✅ System launched successfully!")
        print("🔗 Access the unified admin interface at: http://localhost:7861")
        print("📊 GPU monitoring dashboard: http://localhost:8091")
        print("🤖 LangSmith agent monitoring: http://localhost:8090")
        print("\nPress Ctrl+C to stop the system")
        
        try:
            while True:
                await asyncio.sleep(60)
                # Periodic health checks could go here
        except KeyboardInterrupt:
            print("\n👋 Shutting down system...")
            subprocess.run(['docker-compose', '-f', 'docker-compose.weaviate.yml', 'down'])
            print("✅ System stopped successfully")
    else:
        print("\n❌ Failed to launch system. Check logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
